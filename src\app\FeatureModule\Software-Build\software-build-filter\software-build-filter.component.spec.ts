import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpResponse } from '@angular/common/http';
import { of, Subject, throwError } from 'rxjs';
import { SoftwareBuildFilterComponent } from './software-build-filter.component';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { SoftwareBuildOperationsService } from '../software-build-services/software-build-operations.service';
import { DatePipe } from '@angular/common';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { DeviceOperationService } from '../../Device/DeviceService/Device-Operation/device-operation.service';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { VideoService } from 'src/app/shared/videoservice/video.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { Jsonlist } from 'src/app/model/video/jsonlist.model';
import { SoftwareBuildSearchRequestBody } from 'src/app/model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';
import { SoftwareBuildStatusEnum } from 'src/app/shared/enum/SoftwareBuildStatusEnum';
import { ToastrService } from 'ngx-toastr';
import { ConfirmDialogService } from '../../CommonComponent/confirmationdialog/confirmation.service';

describe('SoftwareBuildFilterComponent', () => {
  let component: SoftwareBuildFilterComponent;
  let fixture: ComponentFixture<SoftwareBuildFilterComponent>;
  let videoService: jasmine.SpyObj<VideoService>;
  let countryCacheService: jasmine.SpyObj<CountryCacheService>;
  let exceptionService: jasmine.SpyObj<ExceptionHandlingService>;
  let softwareBuildOperationsService: jasmine.SpyObj<SoftwareBuildOperationsService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;


  const mockCountryList: CountryListResponse[] = [
    new CountryListResponse(1, 'USA', ['English']),
    new CountryListResponse(2, 'Canada', ['English', 'French'])
  ];

  const mockJsonVersionList: Jsonlist[] = [
    new Jsonlist(1, '1.0.0'),
    new Jsonlist(2, '2.0.0')
  ];

  const mockSoftwareBuildSearchRequestBody = new SoftwareBuildSearchRequestBody(
    'test-version',
    [1, 2],
    deviceTypesEnum.CLIENT_DEVICE,
    true,
    [1],
    'test-part'
  );

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const videoServiceSpy = jasmine.createSpyObj('VideoService', ['getListofJsonVersions']);
    const countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    const exceptionServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    const softwareBuildOperationsServiceSpy = jasmine.createSpyObj('SoftwareBuildOperationsService', [
      'getJsonVersionList', 'setJsonVersionList', 'getCountryList', 'setCountryList',
      'getSoftwareBuildListRefreshSubject', 'callSoftwareBuildListFilterRequestParameterSubject'
    ]);


    await TestBed.configureTestingModule({
      declarations: [SoftwareBuildFilterComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [
        { provide: VideoService, useValue: videoServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionServiceSpy },
        { provide: SoftwareBuildOperationsService, useValue: softwareBuildOperationsServiceSpy },
        CommonsService,
        CommonOperationsService,
        MultiSelectDropDownSettingService,
        LocalStorageService,
        SessionStorageService,
        DatePipe,
        RoleApiCallService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        DeviceOperationService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SoftwareBuildFilterComponent);
    component = fixture.componentInstance;

    videoService = TestBed.inject(VideoService) as jasmine.SpyObj<VideoService>;
    countryCacheService = TestBed.inject(CountryCacheService) as jasmine.SpyObj<CountryCacheService>;
    exceptionService = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
    softwareBuildOperationsService = TestBed.inject(SoftwareBuildOperationsService) as jasmine.SpyObj<SoftwareBuildOperationsService>;

    // Setup default mock returns
    softwareBuildOperationsService.getJsonVersionList.and.returnValue([]);
    softwareBuildOperationsService.getCountryList.and.returnValue([]);
    softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(new Subject());
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize component with API call when isFilterComponentInitWithApicall is true', () => {
      // Setup
      component.isFilterComponentInitWithApicall = true;
      const mockSubject = new Subject<ListingPageReloadSubjectParameter>();

      softwareBuildOperationsService.getJsonVersionList.and.returnValue([]);
      softwareBuildOperationsService.getCountryList.and.returnValue([]);
      softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(mockSubject);
      spyOn(component, 'clearFilter');

      // Act
      component.ngOnInit();

      // Assert
      expect(component.clearFilter).toHaveBeenCalledWith(component.defaultListingPageReloadSubjectParameter);
    });

    it('should initialize component without API call when isFilterComponentInitWithApicall is false', () => {
      // Setup
      component.isFilterComponentInitWithApicall = false;
      const mockSubject = new Subject<ListingPageReloadSubjectParameter>();

      softwareBuildOperationsService.getJsonVersionList.and.returnValue([]);
      softwareBuildOperationsService.getCountryList.and.returnValue([]);
      softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(mockSubject);
      spyOn(component, 'clearFilter');

      // Act
      component.ngOnInit();

      // Assert
      expect(component.clearFilter).not.toHaveBeenCalled();
    });
  });

  describe('initializeDropdownSettings', () => {
    it('should initialize all dropdown settings', () => {
      // Act
      component['initializeDropdownSettings']();
    });
  });

  describe('getInitCall', () => {
    it('should initialize device types and inventory status', async () => {
      // Setup
      softwareBuildOperationsService.getJsonVersionList.and.returnValue(mockJsonVersionList);
      softwareBuildOperationsService.getCountryList.and.returnValue(mockCountryList);
      spyOn(component as any, 'setFilterValue');

      // Act
      await component.getInitCall();

      // Assert
      expect(component.deviceTypes).toEqual(['Client Device', 'Demo Device', 'Above Both', 'Not Associated']);
      expect(component.inventoryStatus).toEqual([SoftwareBuildStatusEnum.ACTIVE, SoftwareBuildStatusEnum.INACTIVE]);
      expect(component.jsonVersionList).toBe(mockJsonVersionList);
      expect(component.countryList).toBe(mockCountryList);
      expect(component['setFilterValue']).toHaveBeenCalled();
    });

    it('should call getJsonVersion when service has empty json version list', async () => {
      // Setup
      softwareBuildOperationsService.getJsonVersionList.and.returnValue([]);
      softwareBuildOperationsService.getCountryList.and.returnValue(mockCountryList);
      spyOn(component as any, 'getJsonVersion');
      spyOn(component as any, 'setFilterValue');

      // Act
      await component.getInitCall();

      // Assert
      expect(component['getJsonVersion']).toHaveBeenCalled();
    });

    it('should call getCountryList when service has empty country list', async () => {
      // Setup
      softwareBuildOperationsService.getJsonVersionList.and.returnValue(mockJsonVersionList);
      softwareBuildOperationsService.getCountryList.and.returnValue([]);
      spyOn(component as any, 'getCountryList').and.returnValue(Promise.resolve());
      spyOn(component as any, 'setFilterValue');

      // Act
      await component.getInitCall();

      // Assert
      expect(component['getCountryList']).toHaveBeenCalled();
    });
  });

  describe('getJsonVersion', () => {
    it('should fetch and cache json versions successfully', () => {
      // Setup
      const mockResponse = new HttpResponse({ body: mockJsonVersionList });
      videoService.getListofJsonVersions.and.returnValue(of(mockResponse));

      // Act
      component['getJsonVersion']();

      // Assert
      expect(videoService.getListofJsonVersions).toHaveBeenCalled();
      expect(softwareBuildOperationsService.setJsonVersionList).toHaveBeenCalledWith(mockJsonVersionList);
      expect(component.jsonVersionList).toEqual(mockJsonVersionList);
    });

    it('should handle error when fetching json versions', () => {
      // Setup
      const mockError = new Error('API Error');
      videoService.getListofJsonVersions.and.returnValue(throwError(() => mockError));

      // Act
      component['getJsonVersion']();

      // Assert
      expect(exceptionService.customErrorMessage).toHaveBeenCalledWith(jasmine.any(Error));
    });
  });

  describe('getCountryList', () => {
    it('should fetch and cache country list successfully', async () => {
      // Setup
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve(mockCountryList));

      // Act
      await component['getCountryList']();

      // Assert
      expect(countryCacheService.getCountryListFromCache).toHaveBeenCalledWith(true);
      expect(softwareBuildOperationsService.setCountryList).toHaveBeenCalledWith(mockCountryList);
      expect(component.countryList).toBe(mockCountryList);
    });
  });

  describe('onInitSubject', () => {
    it('should subscribe to refresh subject and handle clear filter', () => {
      // Setup
      const mockSubject = new Subject<ListingPageReloadSubjectParameter>();
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
      softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(mockSubject);
      spyOn(component, 'clearFilter');

      // Act
      component.onInitSubject();
      mockSubject.next(mockParameter);

      // Assert
      expect(component.clearFilter).toHaveBeenCalledWith(mockParameter);
    });

    it('should subscribe to refresh subject and handle page refresh', () => {
      // Setup
      const mockSubject = new Subject<ListingPageReloadSubjectParameter>();
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(mockSubject);
      spyOn(component as any, 'softWareBuildPageRefresh');

      // Act
      component.onInitSubject();
      mockSubject.next(mockParameter);

      // Assert
      expect(component['softWareBuildPageRefresh']).toHaveBeenCalledWith(mockParameter);
    });

    it('should not process when isReloadData is false', () => {
      // Setup
      const mockSubject = new Subject<ListingPageReloadSubjectParameter>();
      const mockParameter = new ListingPageReloadSubjectParameter(false, true, false, false);
      softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(mockSubject);
      spyOn(component, 'clearFilter');
      spyOn(component as any, 'softWareBuildPageRefresh');

      // Act
      component.onInitSubject();
      mockSubject.next(mockParameter);

      // Assert
      expect(component.clearFilter).not.toHaveBeenCalled();
      expect(component['softWareBuildPageRefresh']).not.toHaveBeenCalled();
    });
  });

  describe('clearFilter', () => {
    it('should reset form and call page refresh', () => {
      // Setup
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
      spyOn(component.filterForm, 'reset');
      spyOn(component as any, 'softWareBuildPageRefresh');

      // Act
      component.clearFilter(mockParameter);

      // Assert
      expect(component.filterForm.reset).toHaveBeenCalled();
      expect(component['softWareBuildPageRefresh']).toHaveBeenCalledWith(mockParameter);
    });

    it('should work without parameter', () => {
      // Setup
      spyOn(component.filterForm, 'reset');
      spyOn(component as any, 'softWareBuildPageRefresh');

      // Act
      component.clearFilter();

      // Assert
      expect(component.filterForm.reset).toHaveBeenCalled();
      expect(component['softWareBuildPageRefresh']).toHaveBeenCalledWith(undefined);
    });
  });

  describe('softWareBuildPageRefresh', () => {
    it('should reset form when invalid and process filter data', () => {
      // Setup
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      spyOn(component.filterForm, 'reset');
      Object.defineProperty(component.filterForm, 'invalid', { value: true, writable: true });

      component.filterForm.patchValue({
        itemNumber: 'test-item',
        partNumber: 'test-part',
        country: mockCountryList,
        jsonVersions: mockJsonVersionList,
        inventoryStatus: ['ACTIVE'],
        deviceType: ['CLIENT_DEVICE']
      });

      // Act
      component['softWareBuildPageRefresh'](mockParameter);

      // Assert
      expect(component.filterForm.reset).toHaveBeenCalled();
      expect(softwareBuildOperationsService.callSoftwareBuildListFilterRequestParameterSubject).toHaveBeenCalled();
    });

    it('should process valid form data without reset', () => {
      // Setup
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      Object.defineProperty(component.filterForm, 'invalid', { value: false, writable: true });

      component.filterForm.patchValue({
        itemNumber: 'test-item',
        partNumber: 'test-part',
        country: mockCountryList,
        jsonVersions: mockJsonVersionList,
        inventoryStatus: ['ACTIVE'],
        deviceType: ['CLIENT_DEVICE']
      });

      spyOn(component.filterForm, 'reset');

      // Act
      component['softWareBuildPageRefresh'](mockParameter);

      // Assert
      expect(component.filterForm.reset).not.toHaveBeenCalled();
      expect(softwareBuildOperationsService.callSoftwareBuildListFilterRequestParameterSubject).toHaveBeenCalled();
    });

    it('should handle null values in form data', () => {
      // Setup
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      Object.defineProperty(component.filterForm, 'invalid', { value: false, writable: true });

      component.filterForm.patchValue({
        itemNumber: null,
        partNumber: null,
        country: null,
        jsonVersions: null,
        inventoryStatus: null,
        deviceType: null
      });
      // Act
      component['softWareBuildPageRefresh'](mockParameter);

      // Assert
      expect(softwareBuildOperationsService.callSoftwareBuildListFilterRequestParameterSubject).toHaveBeenCalled();
    });
  });

  describe('searchInventoryFilter', () => {
    it('should show empty filter message when all fields are empty', () => {
      // Setup
      component.filterForm.patchValue({
        itemNumber: '',
        partNumber: '',
        country: [],
        jsonVersions: [],
        inventoryStatus: [],
        deviceType: []
      });

      // Act
      component.searchInventoryFilter();
    });

    it('should call page refresh when at least one field has value', () => {
      // Setup
      spyOn(component as any, 'softWareBuildPageRefresh');

      component.filterForm.patchValue({
        itemNumber: 'test-item',
        partNumber: '',
        country: [],
        jsonVersions: [],
        inventoryStatus: [],
        deviceType: []
      });

      // Act
      component.searchInventoryFilter();

      // Assert
      expect(component['softWareBuildPageRefresh']).toHaveBeenCalledWith(component.defaultListingPageReloadSubjectParameter);
    });

    it('should call page refresh when deviceType has value', () => {
      // Setup
      spyOn(component as any, 'softWareBuildPageRefresh');

      component.filterForm.patchValue({
        itemNumber: '',
        partNumber: '',
        country: [],
        jsonVersions: [],
        inventoryStatus: [],
        deviceType: ['CLIENT_DEVICE']
      });

      // Act
      component.searchInventoryFilter();

      // Assert
      expect(component['softWareBuildPageRefresh']).toHaveBeenCalledWith(component.defaultListingPageReloadSubjectParameter);
    });
  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from subscription when it exists', () => {
      // Setup
      const mockSubscription = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component.subscriptionForRefreshList = mockSubscription;

      // Act
      component.ngOnDestroy();

      // Assert
      expect(mockSubscription.unsubscribe).toHaveBeenCalled();
    });

    it('should not throw error when subscription is undefined', () => {
      // Setup
      component.subscriptionForRefreshList = undefined;

      // Act & Assert
      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

  describe('setFilterValue', () => {
    it('should set filter values when softwareBuildSearchRequestBody is provided', () => {
      // Setup
      component.softwareBuildSearchRequestBody = mockSoftwareBuildSearchRequestBody;
      component.countryList = mockCountryList;
      component.jsonVersionList = mockJsonVersionList;

      // Act
      component['setFilterValue']();

      // Assert
      expect(component.filterForm.get('itemNumber').value).toBe('test-version');
      expect(component.filterForm.get('partNumber').value).toBe('test-part');
    });

    it('should handle null deviceType in search request body', () => {
      // Setup
      const searchRequestWithNullDeviceType = new SoftwareBuildSearchRequestBody(
        'test-version', [1, 2], null, true, [1], 'test-part'
      );
      component.softwareBuildSearchRequestBody = searchRequestWithNullDeviceType;
      component.countryList = mockCountryList;
      component.jsonVersionList = mockJsonVersionList;

      // Act
      component['setFilterValue']();

      // Assert
      expect(component.filterForm.get('deviceType').value).toEqual([]);
    });

    it('should handle null isActive in search request body', () => {
      // Setup
      const searchRequestWithNullIsActive = new SoftwareBuildSearchRequestBody(
        'test-version', [1, 2], deviceTypesEnum.CLIENT_DEVICE, null, [1], 'test-part'
      );
      component.softwareBuildSearchRequestBody = searchRequestWithNullIsActive;
      component.countryList = mockCountryList;
      component.jsonVersionList = mockJsonVersionList;

      // Act
      component['setFilterValue']();

      // Assert
      expect(component.filterForm.get('inventoryStatus').value).toEqual([]);
    });

    it('should handle empty jsonIds in search request body', () => {
      // Setup
      const searchRequestWithEmptyJsonIds = new SoftwareBuildSearchRequestBody(
        'test-version', [1, 2], deviceTypesEnum.CLIENT_DEVICE, true, [], 'test-part'
      );
      component.softwareBuildSearchRequestBody = searchRequestWithEmptyJsonIds;
      component.countryList = mockCountryList;
      component.jsonVersionList = mockJsonVersionList;

      // Act
      component['setFilterValue']();

      // Assert
      expect(component.filterForm.get('jsonVersions').value).toEqual([]);
    });

    it('should call page refresh when listPageRefreshForbackToDetailPage is true', () => {
      // Setup
      component.listPageRefreshForbackToDetailPage = true;
      component.softwareBuildSearchRequestBody = null;
      spyOn(component as any, 'softWareBuildPageRefresh');

      // Act
      component['setFilterValue']();

      // Assert
      expect(component['softWareBuildPageRefresh']).toHaveBeenCalledWith(component.defaultListingPageReloadSubjectParameter);
    });

    it('should not call page refresh when listPageRefreshForbackToDetailPage is false', () => {
      // Setup
      component.listPageRefreshForbackToDetailPage = false;
      component.softwareBuildSearchRequestBody = null;
      spyOn(component as any, 'softWareBuildPageRefresh');

      // Act
      component['setFilterValue']();

      // Assert
      expect(component['softWareBuildPageRefresh']).not.toHaveBeenCalled();
    });

    it('should not set values when softwareBuildSearchRequestBody is null', () => {
      // Setup
      component.softwareBuildSearchRequestBody = null;
      component.listPageRefreshForbackToDetailPage = false;
      const initialItemNumber = component.filterForm.get('itemNumber').value;

      // Act
      component['setFilterValue']();

      // Assert
      expect(component.filterForm.get('itemNumber').value).toBe(initialItemNumber);
    });
  });
});

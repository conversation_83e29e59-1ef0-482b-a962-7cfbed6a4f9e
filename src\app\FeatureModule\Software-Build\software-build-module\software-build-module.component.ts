import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { SoftwareBuildListResource } from 'src/app/app.constants';

import { SoftwareBuildOperationsService } from '../software-build-services/software-build-operations.service';

@Component({
  selector: 'app-software-build-module',
  templateUrl: './software-build-module.component.html',
  styleUrl: './software-build-module.component.css'
})
export class SoftwareBuildModuleComponent implements OnInit, OnDestroy {

  isSoftwareBuildListingPageDisplay: boolean = true;

  loading: boolean = false;

  softwareBuildListResource = SoftwareBuildListResource;

  constructor(private softwareBuildOperationsService: SoftwareBuildOperationsService) { }

  public ngOnInit(): void {
    this.setDefaultState();
  }

  public ngOnDestroy(): void {
    this.setDefaultState();
  }

  private setDefaultState() {
    this.softwareBuildOperationsService.setSoftwareBuildSearchRequestBodyForListingApi(null);
    this.softwareBuildOperationsService.setIsFilterHiddenForListing(false);
    this.softwareBuildOperationsService.setListPageRefreshForbackToOtherPage(false);
  }

}

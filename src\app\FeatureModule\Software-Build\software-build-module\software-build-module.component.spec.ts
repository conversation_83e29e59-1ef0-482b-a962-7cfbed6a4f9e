import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SoftwareBuildModuleComponent } from './software-build-module.component';
import { UploadScanService } from 'src/app/shared/upload-scan.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { DatePipe, CommonModule } from '@angular/common';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SoftwareBuildSearchRequestBody } from 'src/app/model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';
import { ConfirmDialogService } from '../../CommonComponent/confirmationdialog/confirmation.service';

describe('SoftwareBuildModuleComponent', () => {
  let component: SoftwareBuildModuleComponent;
  let fixture: ComponentFixture<SoftwareBuildModuleComponent>;

  let localStorageSpy: jasmine.SpyObj<LocalStorageService>;
  let sessionStorageSpy: jasmine.SpyObj<SessionStorageService>;

  beforeEach(async () => {
    localStorageSpy = jasmine.createSpyObj('LocalStorageService', ['retrieve', 'store', 'clear']);
    sessionStorageSpy = jasmine.createSpyObj('SessionStorageService', ['retrieve', 'store', 'clear']);

    // Optional: set return values for specific calls
    localStorageSpy.retrieve.and.returnValue(null);
    sessionStorageSpy.retrieve.and.returnValue(null);

    await TestBed.configureTestingModule({
      declarations: [
        SoftwareBuildModuleComponent
      ],
      imports: [CommonModule, NgbPaginationModule, ReactiveFormsModule, FormsModule, NgMultiSelectDropDownModule.forRoot()],
      providers: [
        UploadScanService,
        PermissionService,
        AuthJwtService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        DatePipe,
        { provide: LocalStorageService, useValue: localStorageSpy },
        { provide: SessionStorageService, useValue: sessionStorageSpy },
        commonsProviders(null)
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(SoftwareBuildModuleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // ==================== COMPONENT INITIALIZATION TESTS ====================

  describe('Component Initialization', () => {
    it('should initialize with default values', () => {
      expect(component.sofwareBuildListFilterRequestBody).toBeNull();
      expect(component.isFilterHidden).toBe(false);
      expect(component.isSoftwareBuildListingPageDisplay).toBe(true);
      expect(component.loading).toBe(false);
      expect(component.isFilterComponentInitWithApicall).toBe(true);
    });
  });

  // ==================== UPDATE METHODS TESTS ====================

  describe('Update Methods', () => {
    it('should update software build list filter request body', () => {
      const mockRequestBody = new SoftwareBuildSearchRequestBody('test', [], deviceTypesEnum.CLIENT_DEVICE, true, [], null);

      component.updateSoftwareBuildListFilterRequestBody(mockRequestBody);

      expect(component.sofwareBuildListFilterRequestBody).toBe(mockRequestBody);
    });

    it('should update software build list filter request body with null', () => {
      component.updateSoftwareBuildListFilterRequestBody(null);

      expect(component.sofwareBuildListFilterRequestBody).toBeNull();
    });

    it('should update isFilterComponentInitWithApicall to true', () => {
      component.isFilterComponentInitWithApicall = false;

      component.isFilterComponentInitWithApicallChange(true);

      expect(component.isFilterComponentInitWithApicall).toBe(true);
    });

    it('should update isFilterComponentInitWithApicall to false', () => {
      component.isFilterComponentInitWithApicall = true;

      component.isFilterComponentInitWithApicallChange(false);

      expect(component.isFilterComponentInitWithApicall).toBe(false);
    });

    it('should update isFilterHidden to true', () => {
      component.isFilterHidden = false;

      component.updateIsFilterHidden(true);

      expect(component.isFilterHidden).toBe(true);
    });

    it('should update isFilterHidden to false', () => {
      component.isFilterHidden = true;

      component.updateIsFilterHidden(false);

      expect(component.isFilterHidden).toBe(false);
    });
  });

  // ==================== PROPERTY VALIDATION TESTS ====================

  describe('Property Validation', () => {
    it('should maintain property values after multiple updates', () => {
      const mockRequestBody1 = new SoftwareBuildSearchRequestBody('test1', [], deviceTypesEnum.CLIENT_DEVICE, true, [], null);
      const mockRequestBody2 = new SoftwareBuildSearchRequestBody('test2', [], deviceTypesEnum.DEMO_DEVICE, false, [], null);

      component.updateSoftwareBuildListFilterRequestBody(mockRequestBody1);
      expect(component.sofwareBuildListFilterRequestBody).toBe(mockRequestBody1);

      component.updateSoftwareBuildListFilterRequestBody(mockRequestBody2);
      expect(component.sofwareBuildListFilterRequestBody).toBe(mockRequestBody2);
    });

    it('should handle boolean property changes correctly', () => {
      // Test isFilterComponentInitWithApicall
      component.isFilterComponentInitWithApicallChange(false);
      expect(component.isFilterComponentInitWithApicall).toBe(false);

      component.isFilterComponentInitWithApicallChange(true);
      expect(component.isFilterComponentInitWithApicall).toBe(true);

      // Test isFilterHidden
      component.updateIsFilterHidden(true);
      expect(component.isFilterHidden).toBe(true);

      component.updateIsFilterHidden(false);
      expect(component.isFilterHidden).toBe(false);
    });

    it('should handle edge cases for filter request body updates', () => {
      // Test with undefined
      component.updateSoftwareBuildListFilterRequestBody(undefined);
      expect(component.sofwareBuildListFilterRequestBody).toBeUndefined();

      // Test with complex object
      const complexRequestBody = new SoftwareBuildSearchRequestBody(
        'complex test',
        [1, 2],
        deviceTypesEnum.CLIENT_DEVICE,
        true,
        [1],
        'partNumber123'
      );

      component.updateSoftwareBuildListFilterRequestBody(complexRequestBody);
      expect(component.sofwareBuildListFilterRequestBody).toBe(complexRequestBody);
    });
  });

  // ==================== COMPONENT STATE TESTS ====================

  describe('Component State Management', () => {
    it('should maintain independent state for all properties', () => {
      const mockRequestBody = new SoftwareBuildSearchRequestBody('test', [], deviceTypesEnum.CLIENT_DEVICE, true, [], null);

      // Set all properties to non-default values
      component.updateSoftwareBuildListFilterRequestBody(mockRequestBody);
      component.updateIsFilterHidden(true);
      component.isFilterComponentInitWithApicallChange(false);
      component.isSoftwareBuildListingPageDisplay = false;
      component.loading = true;

      // Verify all properties are set correctly
      expect(component.sofwareBuildListFilterRequestBody).toBe(mockRequestBody);
      expect(component.isFilterHidden).toBe(true);
      expect(component.isFilterComponentInitWithApicall).toBe(false);
      expect(component.isSoftwareBuildListingPageDisplay).toBe(false);
      expect(component.loading).toBe(true);
    });
  });
});

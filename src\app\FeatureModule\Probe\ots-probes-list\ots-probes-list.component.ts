import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { isNullOrUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { ProbeFilterAction } from 'src/app/model/probe/ProbeFilterAction.model';
import { DateTimeDisplayFormat, ITEMS_PER_PAGE, PROBE_APP_VERSION, PROBE_FEATURES, PROBE_LAST_CONNECTED_DATE_AND_TIME, PROBE_LIST_COUNTRY, PROBE_LIST_CREATE_DATE_AND_TIME, PROBE_LIST_CUSTOMER_NAME, PROBE_LIST_EDITABLE, PROBE_LIST_LOCKED, PROBE_LIST_MODIFY_DATE_AND_TIME, PROBE_LIST_SALES_ORDER_NUMBER, PROBE_LIST_SERIAL_NO, PROBE_LIST_STATUS, PROBE_LIST_TYPE, PROBE_OS_TYPE, PROBE_OS_VERSION, PROBE_PRESETS, ProbListResource, SHOW_ENTRY } from '../../../app.constants';
import { ListingPageReloadSubjectParameter } from '../../../model/common/listingPageReloadSubjectParameter.model';
import { ProbeListFilterRequestBody } from '../../../model/probe/ProbeListFilterRequestBody.model';
import { ProbeDetailResponse } from '../../../model/probe/probeDetail.model';
import { JobService } from '../../../shared/Service/JobService/job.service';
import { ProbeApiService } from '../../../shared/Service/ProbeService/probe-api.service';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { DeviceService } from '../../../shared/device.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { ProbeOperationsEnum } from '../../../shared/enum/Operations/ProbeOperations.enum';
import { PermissionAction } from '../../../shared/enum/Permission/permissionAction.enum';
import { DeviceHistoricalData } from '../../../shared/enum/Probe/DeviceHistoricalData.enum';
import { collapseFilterTextEnum } from '../../../shared/enum/collapseFilterButtonText.enum';
import { SalesOrderPdfDownloadService } from '../../../shared/modalservice/salesOrderPdf/sales-order-pdf-download.service';
import { PermissionService } from '../../../shared/permission.service';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { ProbeOperationService } from '../ProbeService/Probe-Operation/probe-operation.service';


@Component({
  selector: 'app-ots-probes',
  templateUrl: './ots-probes-list.component.html',
  styleUrls: ['./ots-probes-list.component.css']
})
export class OtsProbesComponent implements OnInit, OnDestroy {

  // Input/Output properties for communication with parent module
  public isFilterHidden: boolean;

  // Output events to communicate changes back to parent module
  @Output('showProbeDetail') showProbeDetail = new EventEmitter<number>();

  probeListResource = ProbListResource;
  isFilterComponentInitWithApicall: boolean;
  //selection
  probeIdList: number[] = [];
  localProbeDetailResponseList: ProbeDetailResponse[] = [];
  selectedProbeDetailResponseList: ProbeDetailResponse[] = [];

  drpselectsize: number = ITEMS_PER_PAGE;
  probes: ProbeDetailResponse[] = [];
  totalItems: any;
  itemsPerPage: any;
  page: number = 0;
  previousPage: any;
  loading = false;
  totalProbeDisplay: number = 0;
  totalProbes: number = 0;
  deviceIdInput: string;
  displayOts: boolean = true;
  displayOTSDetail: boolean = false;
  displayOtsProbeAddUpdate: boolean = false;
  probeId: number;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;
  probeOperations: string[] = [];

  // show entry selection
  dataSizes: string[] = [];

  // Product status list for display
  productStatusList: any[] = [];

  // List Constants
  dateFormat = DateTimeDisplayFormat;
  serialNo: string = PROBE_LIST_SERIAL_NO;
  salesOrderNumber: string = PROBE_LIST_SALES_ORDER_NUMBER;
  probeType: string = PROBE_LIST_TYPE;
  country: string = PROBE_LIST_COUNTRY;
  customerName: string = PROBE_LIST_CUSTOMER_NAME;
  locked: string = PROBE_LIST_LOCKED;
  editable: string = PROBE_LIST_EDITABLE;
  status: string = PROBE_LIST_STATUS;
  createDateAndTime: string = PROBE_LIST_CREATE_DATE_AND_TIME;
  modifyDateAndTime: string = PROBE_LIST_MODIFY_DATE_AND_TIME;
  showEntry: string = SHOW_ENTRY;
  presets: string = PROBE_PRESETS;
  features: string = PROBE_FEATURES;
  appVersion: string = PROBE_APP_VERSION;
  osVersion: string = PROBE_OS_VERSION;
  osType: string = PROBE_OS_TYPE;
  lastConnectedDateAndTime: string = PROBE_LAST_CONNECTED_DATE_AND_TIME;

  //subject
  subscriptionForCommonloading: Subscription;
  subscriptionForisloading: Subscription;
  subscriptionForDownloadZipFileProbSubject: Subscription;
  subscriptionForProbeListFilterRequestParameter: Subscription;
  subscriptionForProbeListLoading: Subscription;

  //Permission
  probAdminPermission: boolean = false;
  addProbPermission: boolean = true;
  downloadSalesOrderLetterPermission: boolean = false;

  //Add Probe Button
  isAddProbeBtnDisplay: boolean = true;

  //dateTimeDisplayFormat
  dateTimeDisplayFormat = DateTimeDisplayFormat;

  constructor(
    protected jobService: JobService,
    protected deviceService: DeviceService,
    protected router: Router,
    private commonsService: CommonsService,
    private permissionService: PermissionService,
    private authservice: AuthJwtService,
    private downloadService: DownloadService,
    private commonOperationsService: CommonOperationsService,
    private salesOrderPdfDownloadService: SalesOrderPdfDownloadService,
    private probeApiService: ProbeApiService,
    private probeOperationService: ProbeOperationService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
  ) {
    this.itemsPerPage = ITEMS_PER_PAGE;
  }

  ngOnInit() {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.isFilterHidden = this.probeOperationService.getIsFilterHiddenForListing();
      this.page = 0;
      this.probeOperations = this.commonOperationsService.accessProbeOperations(true, false, false, this.probeListResource);
      this.dataSizes = this.commonsService.accessDataSizes();
      this.displayOts = true;
      this.displayOTSDetail = false;
      this.setProbPermission();
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.previousPage = 1;

      // Initialize probe data if user has permission
      if (this.addProbPermission) {
        this.getProbeData();
      }
      this.subjectInit();
      //If Filter hide then page refresh with filter
      if (this.isFilterHidden) {
        this.probeOperationService.setListPageRefreshForbackToOtherPage(false);
        this.filterPageSubjectCallForReloadPage(true, false);
      }
    }

  }

  /**
   * Loading subject to loading start and stop.
   * Download subject -> Open conirmation model after subscribe subject and call download api.
   * Filter subscription for communication with filter component.
   */
  private subjectInit(): void {
    // Probe list loading subscription (following device-list pattern)
    this.subscriptionForProbeListLoading = this.probeOperationService.getProbeListLoadingSubject()?.subscribe((status: boolean) => {
      this.setLoadingStatus(status);
    });

    this.subscriptionForCommonloading = this.commonOperationsService.getCommonLoadingSubject().subscribe((res: boolean) => {
      this.loading = res;
    });
    this.subscriptionForisloading = this.downloadService.getisLoadingSubject().subscribe((res: boolean) => {
      this.loading = res;
    });
    this.subscriptionForDownloadZipFileProbSubject = this.downloadService.getdownloadZipFileForProbSubject().subscribe((res) => {
      this.downloadProbes(res, false);
    });
    debugger
    // Filter subscription (handles both filter changes and operation refreshes)
    this.subscriptionForProbeListFilterRequestParameter = this.probeOperationService.getProbeListFilterRequestParameterSubject()?.subscribe((probeFilterAction: ProbeFilterAction) => {
      if (probeFilterAction.listingPageReloadSubjectParameter.isReloadData) {
        if (probeFilterAction.listingPageReloadSubjectParameter.isDefaultPageNumber) {
          this.probeIdList = [];
          this.selectedProbeDetailResponseList = [];
          this.resetPage();
        }
        this.loadAll(probeFilterAction.probeListFilterRequestBody);
      }
    }
    );
  }

  /**
  * Reset Page
  * <AUTHOR>
  */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  /**
  * Loading Status
  * <AUTHOR>
  */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
  }

  public changeDataSize(datasize: any): void {
    this.loading = true;
    this.probeIdList = [];
    this.selectedProbeDetailResponseList = [];
    this.itemsPerPage = datasize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  /**
   * Get Request Parameter For Search and Export csv
   *
   * <AUTHOR>
   * @returns
   */
  private getParameterForDeviceHistoricalData() {
    // Default to normal data since filter is handled by filter component
    return DeviceHistoricalData.NORMAL;
  }

  /**
   * Probe List API call using probe operation service
   *
   * <AUTHOR>
   */
  public async loadAll(probeListFilterRequestBody: ProbeListFilterRequestBody): Promise<void> {
    this.setLoadingStatus(true);
    this.probeOperationService.setProbeSearchRequestBodyForListingApi(probeListFilterRequestBody);

    const pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage,
    };

    const result = await this.probeOperationService.loadProbeList(probeListFilterRequestBody, pageObj, this.getParameterForDeviceHistoricalData());

    if (result.success) {
      this.probes = result.probes;
      this.totalProbeDisplay = result.totalProbeDisplay;
      this.totalProbes = result.totalProbes;
      this.localProbeDetailResponseList = result.localProbeList;
      this.totalItems = result.totalItems;
      this.page = result.page;
    } else {
      this.probes = [];
      this.totalProbeDisplay = 0;
      this.totalProbes = 0;
      this.localProbeDetailResponseList = [];
      this.totalItems = 0;
    }

    this.setLoadingStatus(false);
    this.defaultSelectAll();
  }

  /**
   * set Prob Permission
   */
  private setProbPermission(): void {
    this.addProbPermission = this.permissionService.getProbPermission(PermissionAction.ADD_PROB_ACTION);
    this.downloadSalesOrderLetterPermission = this.permissionService.getProbPermission(PermissionAction.DOWNLOAD_SALESORDER_LETTER_ACTION);
  }

  /**
  * Probe page initialization
  * <AUTHOR>
  */
  public getProbeData(): void {
    this.page = 0;
    this.dataSizes = this.commonsService.accessDataSizes();
    this.isFilterComponentInitWithApicall = true;
    this.drpselectsize = ITEMS_PER_PAGE;
    this.itemsPerPage = ITEMS_PER_PAGE;
    this.updateIsFilterHidden(this.isFilterHidden);
    this.clearProbeIdCheckBox();
    this.initializeProductStatusList();
  }

  /**
  * Initialize product status list
  * <AUTHOR>
  */
  private initializeProductStatusList(): void {
    this.productStatusList = this.keyValueMappingServiceService.enumOptionToList(ProductStatusEnum);
  }

  /**
  * Refresh Filter
  * <AUTHOR>
  */
  public refreshFilter(): void {
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  /**
  * Call Filter component subject and reload page
  * <AUTHOR>
  * @param isDefaultPageNumber
  * @param isClearFilter
  */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false);
    let probeSearchRequestBody = this.probeOperationService.getProbeSearchRequestBodyForListingApi();
    this.probeOperationService.callRefreshPageSubject(listingPageReloadSubjectParameter, ProbListResource, this.isFilterHidden, probeSearchRequestBody);
  }

  /**
  * Refresh Button Click
  *
  * <AUTHOR>
  */
  public clickOnRefreshButton(): void {
    this.probeOperationService.updateCacheInBackground();
    this.refreshFilter();
  }

  /**
  * Update isFilterHidden from parent component
  * <AUTHOR>
  * @param value
  */
  public updateIsFilterHidden(value: boolean): void {
    this.isFilterHidden = value;
    this.probeOperationService.setIsFilterHiddenForListing(value);
  }

  loadPage(page: number) {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.transition();
    }
  }

  public transition(): void {
    this.loading = true;
    let probe = <HTMLInputElement>document.getElementById("selectAllProbe");
    if (probe != null) {
      probe.checked = false;
    }
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  /**
   * set all probe ids for selection
   * @param inventoryList 
   */
  public setLocalProbe(probeDetailPageResponse: Array<ProbeDetailResponse>): void {
    this.localProbeDetailResponseList = [];
    for (let probeDetail of probeDetailPageResponse) {
      this.localProbeDetailResponseList.push(probeDetail);
    }
    this.defaultSelectAll();
  }

  openProbeDetail(probe: any) {
    this.probeId = probe.id;
    this.showProbeDetail.emit(probe.id);
  }

  /**
   * open OTS Probe List Page
   * 
   * <AUTHOR>
   */
  showOtsProbe(): void {
    this.displayOtsProbeAddUpdate = false;
    this.displayOTSDetail = false;
    this.probeIdList = [];
    this.displayOts = true;
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  otsProbeAddUpdate() {
    this.displayOtsProbeAddUpdate = true;
    this.displayOTSDetail = false;
    this.displayOts = false;
  }

  ngOnDestroy() {
    if (this.subscriptionForCommonloading != undefined) {
      this.subscriptionForCommonloading.unsubscribe();
    }
    if (this.subscriptionForisloading != undefined) {
      this.subscriptionForisloading.unsubscribe();
    }
    if (this.subscriptionForDownloadZipFileProbSubject != undefined) {
      this.subscriptionForDownloadZipFileProbSubject.unsubscribe();
    }
    if (this.subscriptionForProbeListFilterRequestParameter != undefined) {
      this.subscriptionForProbeListFilterRequestParameter.unsubscribe();
    }
    if (this.subscriptionForProbeListLoading != undefined) {
      this.subscriptionForProbeListLoading.unsubscribe();
    }
  }

  /**
  * Toggle Filter
  * Prevents API calls when toggling filter visibility by using cached data
  * <AUTHOR>
  */
  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.updateIsFilterHidden(!this.isFilterHidden);
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
   * show selected rows while pagination
   * @param probeId 
   * @returns 
   */
  public defaultSelectProbe(probeId: number): boolean {
    let index = this.probeIdList.findIndex(id => id == probeId);
    if (index >= 0) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * select/unselect single probe row
   * @param id 
   * @param event 
   */
  public onChangeProbe(probeDetailResponse: ProbeDetailResponse, event: any): void {
    if (event.target.checked) {
      this.probeIdList.push(probeDetailResponse.id);
      this.selectedProbeDetailResponseList.push(probeDetailResponse);
    } else {
      let index = this.probeIdList.findIndex(obj => obj == probeDetailResponse.id);
      this.probeIdList.splice(index, 1);
      let selectedProbeDetailResponseIndex = this.selectedProbeDetailResponseList.findIndex(obj => obj.id == probeDetailResponse.id);
      this.selectedProbeDetailResponseList.splice(selectedProbeDetailResponseIndex, 1);
    }
    this.defaultSelectAll();
  }

  /**
   * clear selection
   */
  public clearProbeIdCheckBox(): void {
    this.probeIdList = [];
    this.selectedProbeDetailResponseList = [];
    let probeCheckbox = (<HTMLInputElement[]><any>document.getElementsByName("probe[]"));
    let probeLength = probeCheckbox.length;
    for (let index = 0; index < probeLength; index++) {
      probeCheckbox[index].checked = false;
    }
    let probe = <HTMLInputElement>document.getElementById("selectAllProbe");
    if (!isNullOrUndefined(probe)) {
      probe.checked = false;
    }
  }

  /**
   * show select All rows while pagination
   */
  public defaultSelectAll(): void {
    let res: boolean = false;
    for (let probeDetailObj of this.localProbeDetailResponseList) {
      let probeIndex = this.probeIdList.findIndex(id => id == probeDetailObj.id);
      if (probeIndex < 0) {
        res = false;
        break;
      } else {
        res = true;
      }
    }
    let selectProbe = <HTMLInputElement>document.getElementById("selectAllProbe");
    if (selectProbe != null) {
      selectProbe.checked = res;
    }
  }

  /**
   * select All rows of current page
   * @param event 
   */
  public selectAllProbe(event: any): void {
    let probedataCheckbox = (<HTMLInputElement[]><any>document.getElementsByName("probe[]"));
    let l = probedataCheckbox.length;
    if (event.target.checked) {
      for (let i = 0; i < l; i++) {
        probedataCheckbox[i].checked = true;
      }
      for (let probeDetailObj of this.localProbeDetailResponseList) {
        let probeindex = this.probeIdList.findIndex(id => id == probeDetailObj.id);
        if (probeindex < 0) {
          this.probeIdList.push(probeDetailObj.id);
          this.selectedProbeDetailResponseList.push(probeDetailObj);
        }
      }
    }
    else {
      for (let i = 0; i < l; i++) {
        probedataCheckbox[i].checked = false;
      }
      for (let probeDetailObj of this.localProbeDetailResponseList) {
        let probeIndexRemove = this.probeIdList.findIndex(id => id == probeDetailObj.id);
        this.probeIdList.splice(probeIndexRemove, 1);
        let probeDetailResponseIndexForRemove = this.selectedProbeDetailResponseList.findIndex(obj => obj.id == probeDetailObj.id);
        this.selectedProbeDetailResponseList.splice(probeDetailResponseIndexForRemove, 1);
      }
    }
  }

  /**
   * probe operations
   * Uses centralized operation handler from common operations service for all operations
   * <AUTHOR>
   * @param event
   */
  public changeProbeOperation(event: any): void {
    const operationName = event.target.value;

    // All operations are now handled by common operations service
    this.probeOperationService.changeOperationForProbe(operationName, ProbListResource, this.probeIdList, this.selectedProbeDetailResponseList);

    let selection = document.getElementById('probeOperation') as HTMLSelectElement;
    selection.value = ProbeOperationsEnum.Probe_Operations;
  }



  /**
  * Download Probe Feature License
  */
  public async downloadProbes(modelStatus: boolean, isLoadingStatus: boolean): Promise<void> {
    if (modelStatus) {
      this.loading = true;
      const probeListFilterRequestBody = this.probeOperationService.getProbeSearchRequestBodyForListingApi();
      await this.probeApiService.dowloadSasUriofFeatureLicenseAsync(this.probeIdList, ProbListResource);
      if (isLoadingStatus) {
        this.loadAll(probeListFilterRequestBody);
      }
    } else {
      this.loading = false;
    }
    this.clearProbeIdCheckBox();
  }


  public downloadPdf(): void {
    this.salesOrderPdfDownloadService.openSalesOrderPdfDownloadModel().then().finally(() => { })
  }
}
